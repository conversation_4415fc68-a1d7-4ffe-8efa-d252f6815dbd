/**
 * 商品混凝土单价公式算法测试
 */

/**
 * 商品混凝土单价计算规则（新版）
 */
const concreteRules = {
  // 附加费字典（需要链接到单元格）
  additionalFees: {
    微膨胀: { value: 25, cell: "K1" },
    补偿收缩: { value: 20, cell: "K2" },
    P8: { value: 15, cell: "K3" },
    细石: { value: 10, cell: "K4" },
  },

  // 不含泵送费用（需要链接到单元格）
  pumpingFee: { value: 12, cell: "K5" },

  // 价格调整规则（需要链接到单元格）
  priceAdjustments: {
    // 基础调整（相对于C30）
    C15_adjustment: { value: -10, cell: "K6" },
    C20_adjustment: { value: -10, cell: "K7" },
    C25_adjustment: { value: -10, cell: "K8" },
    C35_adjustment: { value: 15, cell: "K9" },
    C40_adjustment: { value: 15, cell: "K10" },
    // 高强度调整（相对于前一档次）
    C45_from_C40: { value: 25, cell: "K11" },
    C50_from_C40: { value: 25, cell: "K12" },
    C55_from_C50: { value: 30, cell: "K13" },
  },
};

/**
 * 解析混凝土名称
 * @param {string} concreteName - 混凝土名称
 * @returns {Object} 解析结果
 */
function parseConcreteName(concreteName) {
  const result = {
    grade: "",
    additionalFees: [],
    isPumping: true, // 默认含泵送
  };

  // 提取混凝土标号
  const gradeMatch = concreteName.match(/C(\d+)/);
  if (gradeMatch) {
    result.grade = `C${gradeMatch[1]}`;
  }

  // 检查附加费
  Object.keys(concreteRules.additionalFees).forEach((fee) => {
    if (concreteName.includes(fee)) {
      result.additionalFees.push(fee);
    }
  });

  // C15/C20/C25不含泵送
  if (["C15", "C20", "C25"].includes(result.grade)) {
    result.isPumping = false;
  }

  return result;
}

/**
 * 生成商品混凝土单价公式（新算法）
 * @param {string} concreteName - 混凝土名称
 * @param {string} parentCell - 父级单价单元格（如 H9）
 * @returns {string} Excel公式
 */
function generateConcreteUnitPriceFormula(concreteName, parentCell) {
  const parsed = parseConcreteName(concreteName);

  if (!parsed.grade) {
    return ""; // 无法解析标号，返回空
  }

  let formula = `=${parentCell}`;

  // 1. 处理价格档次调整
  switch (parsed.grade) {
    case "C15":
      formula += `-${concreteRules.priceAdjustments.C15_adjustment.cell}`;
      break;
    case "C20":
      formula += `-${concreteRules.priceAdjustments.C20_adjustment.cell}`;
      break;
    case "C25":
      formula += `-${concreteRules.priceAdjustments.C25_adjustment.cell}`;
      break;
    case "C30":
      // 基准价格，不调整
      break;
    case "C35":
      formula += `+${concreteRules.priceAdjustments.C35_adjustment.cell}`;
      break;
    case "C40":
      formula += `+${concreteRules.priceAdjustments.C40_adjustment.cell}`;
      break;
    case "C45":
      // C45 = C40 + 25，即 C30 + 15 + 25
      formula += `+${concreteRules.priceAdjustments.C40_adjustment.cell}+${concreteRules.priceAdjustments.C45_from_C40.cell}`;
      break;
    case "C50":
      // C50 = C40 + 25，即 C30 + 15 + 25
      formula += `+${concreteRules.priceAdjustments.C40_adjustment.cell}+${concreteRules.priceAdjustments.C50_from_C40.cell}`;
      break;
    case "C55":
      // C55 = C50 + 30，即 C30 + 15 + 25 + 30
      formula += `+${concreteRules.priceAdjustments.C40_adjustment.cell}+${concreteRules.priceAdjustments.C50_from_C40.cell}+${concreteRules.priceAdjustments.C55_from_C50.cell}`;
      break;
  }

  // 2. 处理附加费（按固定顺序：P8, 微膨胀, 补偿收缩, 细石）
  const feeOrder = ["P8", "微膨胀", "补偿收缩", "细石"];
  feeOrder.forEach((fee) => {
    if (parsed.additionalFees.includes(fee)) {
      const feeConfig = concreteRules.additionalFees[fee];
      if (feeConfig) {
        formula += `+${feeConfig.cell}`;
      }
    }
  });

  // 3. 处理泵送费（C15/C20/C25不含泵送）
  if (!parsed.isPumping) {
    formula += `-${concreteRules.pumpingFee.cell}`;
  }

  return formula;
}

/**
 * 测试用例
 */
function runTests() {
  console.log("=".repeat(60));
  console.log("商品混凝土单价公式算法测试");
  console.log("=".repeat(60));

  const testCases = [
    // 基础测试
    { name: "商品混凝土C15", expected: "=H9-K6-K5" },
    { name: "商品混凝土C20", expected: "=H9-K7-K5" },
    { name: "商品混凝土C25", expected: "=H9-K8-K5" },
    { name: "商品混凝土C30", expected: "=H9" },
    { name: "商品混凝土C35", expected: "=H9+K9" },
    { name: "商品混凝土C40", expected: "=H9+K10" },
    { name: "商品混凝土C45", expected: "=H9+K10+K11" },
    { name: "商品混凝土C50", expected: "=H9+K10+K12" },
    { name: "商品混凝土C55", expected: "=H9+K10+K12+K13" },

    // 附加费测试
    { name: "商品混凝土C15细石", expected: "=H9-K6+K4-K5" },
    { name: "商品混凝土C20细石", expected: "=H9-K7+K4-K5" },
    { name: "商品混凝土C30P8", expected: "=H9+K3" },
    { name: "商品混凝土C30微膨胀", expected: "=H9+K1" },
    { name: "商品混凝土C30补偿收缩", expected: "=H9+K2" },
    { name: "商品混凝土C35P8", expected: "=H9+K9+K3" },
    { name: "商品混凝土C35补偿收缩", expected: "=H9+K9+K2" },
    { name: "商品混凝土C40微膨胀", expected: "=H9+K10+K1" },
    { name: "商品混凝土C40P8微膨胀", expected: "=H9+K10+K3+K1" },
    { name: "商品混凝土C45P8", expected: "=H9+K10+K11+K3" },
    { name: "商品混凝土C50 P8", expected: "=H9+K10+K12+K3" },
    { name: "商品混凝土C55P8", expected: "=H9+K10+K12+K13+K3" },
  ];

  let passCount = 0;
  let failCount = 0;

  testCases.forEach((testCase, index) => {
    const result = generateConcreteUnitPriceFormula(testCase.name, "H9");
    const passed = result === testCase.expected;

    console.log(`\n测试 ${index + 1}: ${testCase.name}`);
    console.log(`期望: ${testCase.expected}`);
    console.log(`实际: ${result}`);
    console.log(`结果: ${passed ? "✅ 通过" : "❌ 失败"}`);

    if (passed) {
      passCount++;
    } else {
      failCount++;
    }
  });

  console.log("\n" + "=".repeat(60));
  console.log(`测试总结: 总计 ${testCases.length} 个测试`);
  console.log(`✅ 通过: ${passCount} 个`);
  console.log(`❌ 失败: ${failCount} 个`);
  console.log(`成功率: ${((passCount / testCases.length) * 100).toFixed(1)}%`);
  console.log("=".repeat(60));

  return { passCount, failCount, total: testCases.length };
}

/**
 * 显示规则说明
 */
function showRules() {
  console.log("\n商品混凝土单价计算规则:");
  console.log("1. 价格档次:");
  console.log("   - C15/C20/C25: 在C30基础上减10");
  console.log("   - C30: 基准价格");
  console.log("   - C35/C40: 在C30基础上加15");
  console.log("   - C45/C50: 在C40基础上加25");
  console.log("   - C55: 在C50基础上加30");
  console.log("2. 附加费: 微膨胀(+25), 补偿收缩(+20), P8(+15), 细石(+10)");
  console.log("3. 泵送费: C15/C20/C25不含泵送，需要减去12");
  console.log("4. 所有数值都链接到具体单元格");
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  showRules();
  runTests();
}

module.exports = {
  concreteRules,
  parseConcreteName,
  generateConcreteUnitPriceFormula,
  runTests,
};
