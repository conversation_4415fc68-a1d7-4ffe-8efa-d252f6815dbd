/**
 * 商品混凝土单价公式算法测试
 */

/**
 * 商品混凝土单价计算规则（修正版）
 */
const concreteRules = {
  // 附加费字典（固定表格映射）
  additionalFees: {
    // 序号1-15的附加费项目
    微膨胀: { value: 25, cell: "D2" }, // 1. 微膨胀
    补偿收缩: { value: 20, cell: "D3" }, // 2. 补偿收缩
    P8: { value: 15, cell: "D4" }, // 3. P8
    P10: { value: 20, cell: "D5" }, // 4. P10
    ">P12": { value: 25, cell: "D6" }, // 5. >P12 (P12.5、P13、P20等)
    细石: { value: 10, cell: "D7" }, // 6. 细石
    自密实: { value: 30, cell: "D8" }, // 7. 自密实
    早强: { value: 15, cell: "D9" }, // 8. 早强
    缓凝: { value: 10, cell: "D10" }, // 9. 缓凝
    速凝: { value: 20, cell: "D11" }, // 10. 速凝
    防冻: { value: 25, cell: "D12" }, // 11. 防冻
    抗渗: { value: 15, cell: "D13" }, // 12. 抗渗
    抗硫酸盐: { value: 20, cell: "D14" }, // 13. 抗硫酸盐
    低碱: { value: 10, cell: "D15" }, // 14. 低碱
    // 第15项是不含泵送费用，单独处理
  },

  // 不含泵送费用（C15/C20/C25固定减去的费用）
  pumpingFee: { value: 12, cell: "D16" }, // 15. 不含泵送

  // 每档次调整金额（需要链接到单元格）
  gradeAdjustments: {
    grade_10: { value: 10, cell: "D18" }, // C15/C20/C25每档次减10
    grade_15: { value: 15, cell: "D19" }, // C35/C40每档次加15
    grade_25: { value: 25, cell: "D20" }, // C45/C50每档次加25
    grade_30: { value: 30, cell: "D21" }, // C55每档次加30
  },
};

/**
 * 解析混凝土名称
 * @param {string} concreteName - 混凝土名称
 * @returns {Object} 解析结果
 */
function parseConcreteName(concreteName) {
  const result = {
    grade: "",
    additionalFees: [],
    isPumping: true, // 默认含泵送
  };

  // 提取混凝土标号
  const gradeMatch = concreteName.match(/C(\d+)/);
  if (gradeMatch) {
    result.grade = `C${gradeMatch[1]}`;
  }

  // 检查附加费（按优先级顺序检查，避免重复匹配）
  // 先检查所有P数字模式，确定具体匹配哪个
  const pNumberMatches = concreteName.match(/P(\d+(?:\.\d+)?)/g) || [];
  const matchedPNumbers = pNumberMatches.map((match) => {
    const numberStr = match.substring(1); // 去掉P
    const number = parseFloat(numberStr);
    return { text: match, number: number };
  });

  // 按优先级检查附加费
  const feeOrder = [
    ">P12",
    "P10",
    "P8",
    "微膨胀",
    "补偿收缩",
    "细石",
    "自密实",
    "早强",
    "缓凝",
    "速凝",
    "防冻",
    "抗渗",
    "抗硫酸盐",
    "低碱",
  ];

  feeOrder.forEach((fee) => {
    if (fee === ">P12") {
      // 特殊处理>P12：检查P后面的数字是否大于12
      const hasP12Plus = matchedPNumbers.some((p) => p.number > 12);
      if (hasP12Plus) {
        result.additionalFees.push(fee);
      }
    } else if (fee === "P10") {
      // 精确匹配P10
      const hasP10 = matchedPNumbers.some((p) => p.number === 10);
      if (hasP10) {
        result.additionalFees.push(fee);
      }
    } else if (fee === "P8") {
      // 精确匹配P8
      const hasP8 = matchedPNumbers.some((p) => p.number === 8);
      if (hasP8) {
        result.additionalFees.push(fee);
      }
    } else if (
      concreteRules.additionalFees[fee] &&
      concreteName.includes(fee)
    ) {
      result.additionalFees.push(fee);
    }
  });

  // C15/C20/C25不含泵送
  if (["C15", "C20", "C25"].includes(result.grade)) {
    result.isPumping = false;
  }

  return result;
}

/**
 * 生成商品混凝土单价公式（修正算法）
 * @param {string} concreteName - 混凝土名称
 * @param {string} parentCell - 父级单价单元格（如 H9）
 * @returns {string} Excel公式
 */
function generateConcreteUnitPriceFormula(concreteName, parentCell) {
  const parsed = parseConcreteName(concreteName);

  if (!parsed.grade) {
    return ""; // 无法解析标号，返回空
  }

  let formula = `=${parentCell}`;

  // 1. 处理价格档次调整（保留计算过程逻辑）
  switch (parsed.grade) {
    case "C15":
      // C15 = C30 - 10*3 = C30 - 30
      formula += `-3*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C20":
      // C20 = C30 - 10*2 = C30 - 20
      formula += `-2*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C25":
      // C25 = C30 - 10*1 = C30 - 10
      formula += `-1*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C30":
      // 基准价格，不调整
      break;
    case "C35":
      // C35 = C30 + 15*1 = C30 + 15
      formula += `+1*${concreteRules.gradeAdjustments.grade_15.cell}`;
      break;
    case "C40":
      // C40 = C30 + 15*2 = C30 + 30
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}`;
      break;
    case "C45":
      // C45 = C40 + 25*1 = C30 + 15*2 + 25*1
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+1*${concreteRules.gradeAdjustments.grade_25.cell}`;
      break;
    case "C50":
      // C50 = C40 + 25*2 = C30 + 15*2 + 25*2
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+2*${concreteRules.gradeAdjustments.grade_25.cell}`;
      break;
    case "C55":
      // C55 = C50 + 30*1 = C30 + 15*2 + 25*2 + 30*1
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+2*${concreteRules.gradeAdjustments.grade_25.cell}+1*${concreteRules.gradeAdjustments.grade_30.cell}`;
      break;
  }

  // 2. 处理附加费（按固定顺序）
  const feeOrder = [
    ">P12",
    "P10",
    "P8",
    "微膨胀",
    "补偿收缩",
    "细石",
    "自密实",
    "早强",
    "缓凝",
    "速凝",
    "防冻",
    "抗渗",
    "抗硫酸盐",
    "低碱",
  ];
  feeOrder.forEach((fee) => {
    if (parsed.additionalFees.includes(fee)) {
      const feeConfig = concreteRules.additionalFees[fee];
      if (feeConfig) {
        formula += `+${feeConfig.cell}`;
      }
    }
  });

  // 3. 处理泵送费（C15/C20/C25不含泵送）
  if (!parsed.isPumping) {
    formula += `-${concreteRules.pumpingFee.cell}`;
  }

  return formula;
}

/**
 * 测试用例
 */
function runTests() {
  console.log("=".repeat(60));
  console.log("商品混凝土单价公式算法测试");
  console.log("=".repeat(60));

  const testCases = [
    // 基础测试（保留计算过程逻辑）
    { name: "商品混凝土C15", expected: "=H9-3*D18-D16" },
    { name: "商品混凝土C20", expected: "=H9-2*D18-D16" },
    { name: "商品混凝土C25", expected: "=H9-1*D18-D16" },
    { name: "商品混凝土C30", expected: "=H9" },
    { name: "商品混凝土C35", expected: "=H9+1*D19" },
    { name: "商品混凝土C40", expected: "=H9+2*D19" },
    { name: "商品混凝土C45", expected: "=H9+2*D19+1*D20" },
    { name: "商品混凝土C50", expected: "=H9+2*D19+2*D20" },
    { name: "商品混凝土C55", expected: "=H9+2*D19+2*D20+1*D21" },

    // 附加费测试
    { name: "商品混凝土C15细石", expected: "=H9-3*D18+D7-D16" },
    { name: "商品混凝土C20细石", expected: "=H9-2*D18+D7-D16" },
    { name: "商品混凝土C30P8", expected: "=H9+D4" },
    { name: "商品混凝土C30微膨胀", expected: "=H9+D2" },
    { name: "商品混凝土C30补偿收缩", expected: "=H9+D3" },
    { name: "商品混凝土C35P8", expected: "=H9+1*D19+D4" },
    { name: "商品混凝土C35补偿收缩", expected: "=H9+1*D19+D3" },
    { name: "商品混凝土C40微膨胀", expected: "=H9+2*D19+D2" },
    { name: "商品混凝土C40P8微膨胀", expected: "=H9+2*D19+D4+D2" },
    { name: "商品混凝土C45P8", expected: "=H9+2*D19+1*D20+D4" },
    { name: "商品混凝土C50 P8", expected: "=H9+2*D19+2*D20+D4" },
    { name: "商品混凝土C55P8", expected: "=H9+2*D19+2*D20+1*D21+D4" },

    // 新增测试：>P12系列（P后数字大于12）
    { name: "商品混凝土C30P13", expected: "=H9+D6" },
    { name: "商品混凝土C40P12.5", expected: "=H9+2*D19+D6" },
    { name: "商品混凝土C35P20", expected: "=H9+1*D19+D6" },
    { name: "商品混凝土C30P15", expected: "=H9+D6" },
    { name: "商品混凝土C25P25", expected: "=H9-1*D18+D6-D16" },
    { name: "商品混凝土C40P100", expected: "=H9+2*D19+D6" },

    // 边界测试：P12及以下不应该匹配>P12
    { name: "商品混凝土C30P12", expected: "=H9" },
    { name: "商品混凝土C30P10", expected: "=H9+D5" }, // 应该匹配P10
    { name: "商品混凝土C30P8", expected: "=H9+D4" }, // 应该匹配P8
  ];

  let passCount = 0;
  let failCount = 0;

  testCases.forEach((testCase, index) => {
    const result = generateConcreteUnitPriceFormula(testCase.name, "H9");
    const passed = result === testCase.expected;

    console.log(`\n测试 ${index + 1}: ${testCase.name}`);
    console.log(`期望: ${testCase.expected}`);
    console.log(`实际: ${result}`);
    console.log(`结果: ${passed ? "✅ 通过" : "❌ 失败"}`);

    if (passed) {
      passCount++;
    } else {
      failCount++;
    }
  });

  console.log("\n" + "=".repeat(60));
  console.log(`测试总结: 总计 ${testCases.length} 个测试`);
  console.log(`✅ 通过: ${passCount} 个`);
  console.log(`❌ 失败: ${failCount} 个`);
  console.log(`成功率: ${((passCount / testCases.length) * 100).toFixed(1)}%`);
  console.log("=".repeat(60));

  return { passCount, failCount, total: testCases.length };
}

/**
 * 显示规则说明
 */
function showRules() {
  console.log("\n商品混凝土单价计算规则（修正版）:");
  console.log("1. 价格档次（每个档次指强度等级差异）:");
  console.log("   - C15: C30 - 10×3 = C30 - 30 (3个档次)");
  console.log("   - C20: C30 - 10×2 = C30 - 20 (2个档次)");
  console.log("   - C25: C30 - 10×1 = C30 - 10 (1个档次)");
  console.log("   - C30: 基准价格");
  console.log("   - C35: C30 + 15×1 = C30 + 15 (1个档次)");
  console.log("   - C40: C30 + 15×2 = C30 + 30 (2个档次)");
  console.log("   - C45: C40 + 25×1 = C30 + 55 (基于C40)");
  console.log("   - C50: C40 + 25×2 = C30 + 80 (基于C40)");
  console.log("   - C55: C50 + 30×1 = C30 + 110 (基于C50)");
  console.log("2. 附加费: 微膨胀(+25), 补偿收缩(+20), P8(+15), 细石(+10)");
  console.log("3. 泵送费: C15/C20/C25不含泵送，需要减去12");
  console.log("4. 所有数值都链接到具体单元格，保留计算过程逻辑");
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  showRules();
  runTests();
}

module.exports = {
  concreteRules,
  parseConcreteName,
  generateConcreteUnitPriceFormula,
  runTests,
};
