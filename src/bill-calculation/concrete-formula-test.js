/**
 * 商品混凝土单价公式算法测试
 */

/**
 * 商品混凝土单价计算规则（修正版）
 */
const concreteRules = {
  // 附加费字典（需要链接到单元格）
  additionalFees: {
    微膨胀: { value: 25, cell: "K1" },
    补偿收缩: { value: 20, cell: "K2" },
    P8: { value: 15, cell: "K3" },
    细石: { value: 10, cell: "K4" },
  },

  // 不含泵送费用（需要链接到单元格）
  pumpingFee: { value: 12, cell: "K5" },

  // 每档次调整金额（需要链接到单元格）
  gradeAdjustments: {
    // 每档次调整金额
    grade_10: { value: 10, cell: "K6" }, // C15/C20/C25每档次减10
    grade_15: { value: 15, cell: "K7" }, // C35/C40每档次加15
    grade_25: { value: 25, cell: "K8" }, // C45/C50每档次加25
    grade_30: { value: 30, cell: "K9" }, // C55每档次加30
  },
};

/**
 * 解析混凝土名称
 * @param {string} concreteName - 混凝土名称
 * @returns {Object} 解析结果
 */
function parseConcreteName(concreteName) {
  const result = {
    grade: "",
    additionalFees: [],
    isPumping: true, // 默认含泵送
  };

  // 提取混凝土标号
  const gradeMatch = concreteName.match(/C(\d+)/);
  if (gradeMatch) {
    result.grade = `C${gradeMatch[1]}`;
  }

  // 检查附加费
  Object.keys(concreteRules.additionalFees).forEach((fee) => {
    if (concreteName.includes(fee)) {
      result.additionalFees.push(fee);
    }
  });

  // C15/C20/C25不含泵送
  if (["C15", "C20", "C25"].includes(result.grade)) {
    result.isPumping = false;
  }

  return result;
}

/**
 * 生成商品混凝土单价公式（修正算法）
 * @param {string} concreteName - 混凝土名称
 * @param {string} parentCell - 父级单价单元格（如 H9）
 * @returns {string} Excel公式
 */
function generateConcreteUnitPriceFormula(concreteName, parentCell) {
  const parsed = parseConcreteName(concreteName);

  if (!parsed.grade) {
    return ""; // 无法解析标号，返回空
  }

  let formula = `=${parentCell}`;

  // 1. 处理价格档次调整（保留计算过程逻辑）
  switch (parsed.grade) {
    case "C15":
      // C15 = C30 - 10*3 = C30 - 30
      formula += `-3*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C20":
      // C20 = C30 - 10*2 = C30 - 20
      formula += `-2*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C25":
      // C25 = C30 - 10*1 = C30 - 10
      formula += `-1*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C30":
      // 基准价格，不调整
      break;
    case "C35":
      // C35 = C30 + 15*1 = C30 + 15
      formula += `+1*${concreteRules.gradeAdjustments.grade_15.cell}`;
      break;
    case "C40":
      // C40 = C30 + 15*2 = C30 + 30
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}`;
      break;
    case "C45":
      // C45 = C40 + 25*1 = C30 + 15*2 + 25*1
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+1*${concreteRules.gradeAdjustments.grade_25.cell}`;
      break;
    case "C50":
      // C50 = C40 + 25*2 = C30 + 15*2 + 25*2
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+2*${concreteRules.gradeAdjustments.grade_25.cell}`;
      break;
    case "C55":
      // C55 = C50 + 30*1 = C30 + 15*2 + 25*2 + 30*1
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+2*${concreteRules.gradeAdjustments.grade_25.cell}+1*${concreteRules.gradeAdjustments.grade_30.cell}`;
      break;
  }

  // 2. 处理附加费（按固定顺序：P8, 微膨胀, 补偿收缩, 细石）
  const feeOrder = ["P8", "微膨胀", "补偿收缩", "细石"];
  feeOrder.forEach((fee) => {
    if (parsed.additionalFees.includes(fee)) {
      const feeConfig = concreteRules.additionalFees[fee];
      if (feeConfig) {
        formula += `+${feeConfig.cell}`;
      }
    }
  });

  // 3. 处理泵送费（C15/C20/C25不含泵送）
  if (!parsed.isPumping) {
    formula += `-${concreteRules.pumpingFee.cell}`;
  }

  return formula;
}

/**
 * 测试用例
 */
function runTests() {
  console.log("=".repeat(60));
  console.log("商品混凝土单价公式算法测试");
  console.log("=".repeat(60));

  const testCases = [
    // 基础测试（保留计算过程逻辑）
    { name: "商品混凝土C15", expected: "=H9-3*K6-K5" },
    { name: "商品混凝土C20", expected: "=H9-2*K6-K5" },
    { name: "商品混凝土C25", expected: "=H9-1*K6-K5" },
    { name: "商品混凝土C30", expected: "=H9" },
    { name: "商品混凝土C35", expected: "=H9+1*K7" },
    { name: "商品混凝土C40", expected: "=H9+2*K7" },
    { name: "商品混凝土C45", expected: "=H9+2*K7+1*K8" },
    { name: "商品混凝土C50", expected: "=H9+2*K7+2*K8" },
    { name: "商品混凝土C55", expected: "=H9+2*K7+2*K8+1*K9" },

    // 附加费测试
    { name: "商品混凝土C15细石", expected: "=H9-3*K6+K4-K5" },
    { name: "商品混凝土C20细石", expected: "=H9-2*K6+K4-K5" },
    { name: "商品混凝土C30P8", expected: "=H9+K3" },
    { name: "商品混凝土C30微膨胀", expected: "=H9+K1" },
    { name: "商品混凝土C30补偿收缩", expected: "=H9+K2" },
    { name: "商品混凝土C35P8", expected: "=H9+1*K7+K3" },
    { name: "商品混凝土C35补偿收缩", expected: "=H9+1*K7+K2" },
    { name: "商品混凝土C40微膨胀", expected: "=H9+2*K7+K1" },
    { name: "商品混凝土C40P8微膨胀", expected: "=H9+2*K7+K3+K1" },
    { name: "商品混凝土C45P8", expected: "=H9+2*K7+1*K8+K3" },
    { name: "商品混凝土C50 P8", expected: "=H9+2*K7+2*K8+K3" },
    { name: "商品混凝土C55P8", expected: "=H9+2*K7+2*K8+1*K9+K3" },
  ];

  let passCount = 0;
  let failCount = 0;

  testCases.forEach((testCase, index) => {
    const result = generateConcreteUnitPriceFormula(testCase.name, "H9");
    const passed = result === testCase.expected;

    console.log(`\n测试 ${index + 1}: ${testCase.name}`);
    console.log(`期望: ${testCase.expected}`);
    console.log(`实际: ${result}`);
    console.log(`结果: ${passed ? "✅ 通过" : "❌ 失败"}`);

    if (passed) {
      passCount++;
    } else {
      failCount++;
    }
  });

  console.log("\n" + "=".repeat(60));
  console.log(`测试总结: 总计 ${testCases.length} 个测试`);
  console.log(`✅ 通过: ${passCount} 个`);
  console.log(`❌ 失败: ${failCount} 个`);
  console.log(`成功率: ${((passCount / testCases.length) * 100).toFixed(1)}%`);
  console.log("=".repeat(60));

  return { passCount, failCount, total: testCases.length };
}

/**
 * 显示规则说明
 */
function showRules() {
  console.log("\n商品混凝土单价计算规则（修正版）:");
  console.log("1. 价格档次（每个档次指强度等级差异）:");
  console.log("   - C15: C30 - 10×3 = C30 - 30 (3个档次)");
  console.log("   - C20: C30 - 10×2 = C30 - 20 (2个档次)");
  console.log("   - C25: C30 - 10×1 = C30 - 10 (1个档次)");
  console.log("   - C30: 基准价格");
  console.log("   - C35: C30 + 15×1 = C30 + 15 (1个档次)");
  console.log("   - C40: C30 + 15×2 = C30 + 30 (2个档次)");
  console.log("   - C45: C40 + 25×1 = C30 + 55 (基于C40)");
  console.log("   - C50: C40 + 25×2 = C30 + 80 (基于C40)");
  console.log("   - C55: C50 + 30×1 = C30 + 110 (基于C50)");
  console.log("2. 附加费: 微膨胀(+25), 补偿收缩(+20), P8(+15), 细石(+10)");
  console.log("3. 泵送费: C15/C20/C25不含泵送，需要减去12");
  console.log("4. 所有数值都链接到具体单元格，保留计算过程逻辑");
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  showRules();
  runTests();
}

module.exports = {
  concreteRules,
  parseConcreteName,
  generateConcreteUnitPriceFormula,
  runTests,
};
