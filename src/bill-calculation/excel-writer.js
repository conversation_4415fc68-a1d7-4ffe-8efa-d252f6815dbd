const ExcelJS = require("exceljs");
const path = require("path");

/**
 * 创建 3-分部分项成本测算 Excel工作簿
 * @returns {Object} 包含工作簿和工作表的对象
 */
function createCostCalculationWorkbook() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("3-分部分项成本测算");

  // 设置表头 - 只设置A~H列
  worksheet.columns = [
    { header: "序号", key: "serialNo", width: 8 }, // A列
    { header: "分部", key: "division", width: 15 }, // B列
    { header: "目标", key: "target", width: 30 }, // C列
    { header: "项目特征", key: "feature", width: 40 }, // D列
    { header: "计量单位", key: "unit", width: 12 }, // E列
    { header: "工程量", key: "quantity", width: 15 }, // F列
    { header: "含量", key: "content", width: 12 }, // G列
    { header: "测算成本量", key: "costAmount", width: 15 }, // H列
  ];

  // 设置表头样式
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  return { workbook, worksheet };
}

/**
 * 添加数据行到工作表
 * @param {Object} worksheet - Excel工作表对象
 * @param {Array} processedData - 处理后的数据数组
 */
function addDataRows(worksheet, processedData) {
  let rowIndex = 2; // 从第2行开始（第1行是表头）
  let parentRowIndex = null; // 记录父级行的行号

  processedData.forEach((item, index) => {
    // 添加数据行 - 只添加A~H列
    const row = worksheet.addRow({
      serialNo: item.序号,
      division: item.分部,
      target: item.目标,
      feature: item.项目特征,
      unit: item.计量单位,
      quantity: item.工程量,
      content: item.含量,
      costAmount: "", // H列暂时为空，后面添加公式
    });

    // 根据数据级别处理公式
    if (item.数据级别 === 2) {
      // 2级数据（目标）- 记录为父级行
      parentRowIndex = rowIndex;
    } else if (["人", "材", "专"].includes(item.分部) && parentRowIndex) {
      // 3级数据（人、材、专）- 使用父级行的工程量 × 当前行的含量
      const parentFColumn = `F${parentRowIndex}`; // 父级行的工程量（F列）
      const currentGColumn = `G${rowIndex}`; // 当前行的含量（G列）
      const formula = `=${parentFColumn}*${currentGColumn}`;

      row.getCell(8).value = { formula: formula }; // H列

      console.log(
        `  \x1b[36m[公式] \x1b[0m第${rowIndex}行 H列: \x1b[33m${formula}\x1b[0m (${item.分部}: ${item.目标}) - 父级第${parentRowIndex}行`
      );
    }

    // 设置数据行样式
    setRowStyles(worksheet, rowIndex, item);

    rowIndex++;
  });

  return rowIndex - 1; // 返回总行数
}

/**
 * 设置行样式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} rowIndex - 行索引
 * @param {Object} item - 数据项
 */
function setRowStyles(worksheet, rowIndex, item) {
  const row = worksheet.getRow(rowIndex);

  // 根据数据级别设置不同样式
  switch (item.数据级别) {
    case 1: // 分部级别
      row.font = { bold: true };
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFD0D0D0" }, // 深灰色
      };
      break;
    case 2: // 目标级别
      row.font = { bold: false };
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF0F0F0" }, // 浅灰色
      };
      break;
    case 3: // 人/材/专级别
      row.font = { bold: false };
      // 不设置背景色，保持默认白色
      break;
  }

  // 设置数字格式
  setNumberFormats(row, item);
}

/**
 * 设置数字格式
 * @param {Object} row - Excel行对象
 * @param {Object} item - 数据项
 */
function setNumberFormats(row, item) {
  // F列：工程量 - 数字格式
  if (item.工程量 && !isNaN(parseFloat(item.工程量))) {
    row.getCell(6).numFmt = "0.00";
  }

  // G列：含量 - 数字格式
  if (item.含量 && !isNaN(parseFloat(item.含量))) {
    row.getCell(7).numFmt = "0.00";
  }

  // H列：测算成本量 - 数字格式
  row.getCell(8).numFmt = "0.00";
}

/**
 * 设置单元格边框和对齐方式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} rowCount - 总行数
 */
function setCellStyles(worksheet, rowCount) {
  // 设置所有单元格的边框和对齐方式
  for (let i = 1; i <= rowCount; i++) {
    for (let j = 1; j <= 8; j++) {
      // A到H列
      const cell = worksheet.getCell(i, j);

      // 设置边框
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // 设置对齐方式
      cell.alignment = {
        vertical: "middle",
        wrapText: true,
      };

      // 数字列居中对齐
      if (j >= 5 && j <= 8) {
        // E到H列（计量单位、工程量、含量、测算成本量）
        cell.alignment.horizontal = "center";
      }
    }
  }
}

/**
 * 生成 Excel 文件
 * @param {Array} processedData - 处理后的数据数组
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 文件名（不含扩展名）
 * @returns {Promise<string>} 生成的Excel文件路径
 */
async function generateCostCalculationExcel(
  processedData,
  outputDir,
  fileName
) {
  console.log(
    `\n\x1b[36m[生成] \x1b[0m开始生成 Excel 文件: \x1b[33m${fileName}.xlsx\x1b[0m`
  );

  // 创建工作簿和工作表
  const { workbook, worksheet } = createCostCalculationWorkbook();

  // 添加数据行
  const rowCount = addDataRows(worksheet, processedData);

  // 设置单元格样式
  setCellStyles(worksheet, rowCount);

  // 生成输出文件路径
  const outputFilePath = path.join(outputDir, `${fileName}.xlsx`);

  try {
    // 保存Excel文件
    await workbook.xlsx.writeFile(outputFilePath);

    console.log(
      `\x1b[32m[成功] \x1b[0m Excel文件已保存: \x1b[33m${outputFilePath}\x1b[0m`
    );
    console.log(
      `\x1b[36m[信息] \x1b[0m总行数: \x1b[32m${rowCount}\x1b[0m (包含表头)`
    );

    return outputFilePath;
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m生成Excel文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 创建材料清单工作簿
 * @returns {Object} 包含工作簿和工作表的对象
 */
function createMaterialListWorkbook() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("4-材料清单");

  // 设置表头 - A~L列
  worksheet.columns = [
    { header: "", key: "empty", width: 8 }, // A列 - 空列
    { header: "分部工程", key: "division", width: 15 }, // B列
    { header: "原测算清单", key: "originalList", width: 30 }, // C列
    { header: "规格型号", key: "specification", width: 25 }, // D列
    { header: "计量单位", key: "unit", width: 12 }, // E列
    { header: "测算含量", key: "content", width: 12 }, // F列
    { header: "测算成本量", key: "costAmount", width: 15 }, // G列
    { header: "测算单价", key: "unitPrice", width: 12 }, // H列
    { header: "测算金额", key: "totalAmount", width: 15 }, // I列
    { header: "税率（%）", key: "taxRate", width: 12 }, // J列
    { header: "测算说明", key: "description", width: 20 }, // K列
    { header: "标签1", key: "tag1", width: 15 }, // L列
  ];

  // 设置表头样式
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  return { workbook, worksheet };
}

/**
 * 商品混凝土单价计算规则（完整版）
 */
const concreteRules = {
  // 附加费字典（固定表格映射）
  additionalFees: {
    // 序号1-15的附加费项目
    微膨胀: { value: 25, cell: "'4-附加费'!D2" }, // 1. 微膨胀
    补偿收缩: { value: 20, cell: "'4-附加费'!D3" }, // 2. 补偿收缩
    P8: { value: 15, cell: "'4-附加费'!D4" }, // 3. P8
    P10: { value: 20, cell: "'4-附加费'!D5" }, // 4. P10
    ">P12": { value: 25, cell: "'4-附加费'!D6" }, // 5. >P12 (P12.5、P13、P20等)
    细石: { value: 10, cell: "'4-附加费'!D7" }, // 6. 细石
    自密实: { value: 30, cell: "'4-附加费'!D8" }, // 7. 自密实
    早强: { value: 15, cell: "'4-附加费'!D9" }, // 8. 早强
    缓凝: { value: 10, cell: "'4-附加费'!D10" }, // 9. 缓凝
    速凝: { value: 20, cell: "'4-附加费'!D11" }, // 10. 速凝
    防冻: { value: 25, cell: "'4-附加费'!D12" }, // 11. 防冻
    抗渗: { value: 15, cell: "'4-附加费'!D13" }, // 12. 抗渗
    抗硫酸盐: { value: 20, cell: "'4-附加费'!D14" }, // 13. 抗硫酸盐
    低碱: { value: 10, cell: "'4-附加费'!D15" }, // 14. 低碱
    // 第15项是不含泵送费用，单独处理
  },

  // 不含泵送费用（C15/C20/C25固定减去的费用）
  pumpingFee: { value: 12, cell: "'4-附加费'!D16" }, // 15. 不含泵送

  // 每档次调整金额（链接到"4-附加费"工作表）
  gradeAdjustments: {
    grade_10: { value: 10, cell: "'4-附加费'!D18" }, // C15/C20/C25每档次减10
    grade_15: { value: 15, cell: "'4-附加费'!D19" }, // C35/C40每档次加15
    grade_25: { value: 25, cell: "'4-附加费'!D20" }, // C45/C50每档次加25
    grade_30: { value: 30, cell: "'4-附加费'!D21" }, // C55每档次加30
  },
};

/**
 * 解析混凝土标号和附加费
 * @param {string} concreteName - 混凝土名称
 * @returns {Object} 解析结果
 */
function parseConcreteName(concreteName) {
  const result = {
    grade: "",
    additionalFees: [],
    isPumping: true, // 默认含泵送
  };

  // 提取混凝土标号
  const gradeMatch = concreteName.match(/C(\d+)/);
  if (gradeMatch) {
    result.grade = `C${gradeMatch[1]}`;
  }

  // 检查附加费（按优先级顺序检查，避免重复匹配）
  // 先检查所有P数字模式，确定具体匹配哪个
  const pNumberMatches = concreteName.match(/P(\d+(?:\.\d+)?)/g) || [];
  const matchedPNumbers = pNumberMatches.map((match) => {
    const numberStr = match.substring(1); // 去掉P
    const number = parseFloat(numberStr);
    return { text: match, number: number };
  });

  // 按优先级检查附加费
  const feeOrder = [
    ">P12",
    "P10",
    "P8",
    "微膨胀",
    "补偿收缩",
    "细石",
    "自密实",
    "早强",
    "缓凝",
    "速凝",
    "防冻",
    "抗渗",
    "抗硫酸盐",
    "低碱",
  ];

  feeOrder.forEach((fee) => {
    if (fee === ">P12") {
      // 特殊处理>P12：检查P后面的数字是否大于12
      const hasP12Plus = matchedPNumbers.some((p) => p.number > 12);
      if (hasP12Plus) {
        result.additionalFees.push(fee);
      }
    } else if (fee === "P10") {
      // 精确匹配P10
      const hasP10 = matchedPNumbers.some((p) => p.number === 10);
      if (hasP10) {
        result.additionalFees.push(fee);
      }
    } else if (fee === "P8") {
      // 精确匹配P8
      const hasP8 = matchedPNumbers.some((p) => p.number === 8);
      if (hasP8) {
        result.additionalFees.push(fee);
      }
    } else if (
      concreteRules.additionalFees[fee] &&
      concreteName.includes(fee)
    ) {
      result.additionalFees.push(fee);
    }
  });

  // C15/C20/C25不含泵送
  if (["C15", "C20", "C25"].includes(result.grade)) {
    result.isPumping = false;
  }

  return result;
}

/**
 * 生成商品混凝土单价公式
 * @param {string} concreteName - 混凝土名称
 * @param {number} parentRowIndex - 父级行索引
 * @param {Object} additionalFeeCells - 附加费单元格位置映射
 * @returns {string} Excel公式
 */
function generateConcreteUnitPriceFormula(
  concreteName,
  parentRowIndex,
  additionalFeeCells = {}
) {
  const parsed = parseConcreteName(concreteName);

  if (!parsed.grade) {
    return ""; // 无法解析标号，返回空
  }

  const parentCell = `H${parentRowIndex}`; // 父级单价（C30基准价格）
  let formula = `=${parentCell}`;

  // 1. 处理价格档次调整（保留计算过程逻辑）
  switch (parsed.grade) {
    case "C15":
      // C15 = C30 - 10*3 = C30 - 30
      formula += `-3*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C20":
      // C20 = C30 - 10*2 = C30 - 20
      formula += `-2*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C25":
      // C25 = C30 - 10*1 = C30 - 10
      formula += `-1*${concreteRules.gradeAdjustments.grade_10.cell}`;
      break;
    case "C30":
      // 基准价格，不调整
      break;
    case "C35":
      // C35 = C30 + 15*1 = C30 + 15
      formula += `+1*${concreteRules.gradeAdjustments.grade_15.cell}`;
      break;
    case "C40":
      // C40 = C30 + 15*2 = C30 + 30
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}`;
      break;
    case "C45":
      // C45 = C40 + 25*1 = C30 + 15*2 + 25*1
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+1*${concreteRules.gradeAdjustments.grade_25.cell}`;
      break;
    case "C50":
      // C50 = C40 + 25*2 = C30 + 15*2 + 25*2
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+2*${concreteRules.gradeAdjustments.grade_25.cell}`;
      break;
    case "C55":
      // C55 = C50 + 30*1 = C30 + 15*2 + 25*2 + 30*1
      formula += `+2*${concreteRules.gradeAdjustments.grade_15.cell}+2*${concreteRules.gradeAdjustments.grade_25.cell}+1*${concreteRules.gradeAdjustments.grade_30.cell}`;
      break;
  }

  // 2. 处理附加费（按固定顺序）
  const feeOrder = [
    ">P12",
    "P10",
    "P8",
    "微膨胀",
    "补偿收缩",
    "细石",
    "自密实",
    "早强",
    "缓凝",
    "速凝",
    "防冻",
    "抗渗",
    "抗硫酸盐",
    "低碱",
  ];
  feeOrder.forEach((fee) => {
    if (parsed.additionalFees.includes(fee)) {
      const feeConfig = concreteRules.additionalFees[fee];
      if (feeConfig) {
        formula += `+${feeConfig.cell}`;
      }
    }
  });

  // 3. 处理泵送费（C15/C20/C25不含泵送）
  if (!parsed.isPumping) {
    formula += `-${concreteRules.pumpingFee.cell}`;
  }

  return formula;
}

module.exports = {
  createCostCalculationWorkbook,
  addDataRows,
  setCellStyles,
  generateCostCalculationExcel,
  createMaterialListWorkbook,
  parseConcreteName,
  generateConcreteUnitPriceFormula,
  concreteRules,
};
