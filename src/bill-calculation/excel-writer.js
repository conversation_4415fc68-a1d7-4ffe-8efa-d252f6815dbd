const ExcelJS = require("exceljs");
const path = require("path");

/**
 * 创建 3-分部分项成本测算 Excel工作簿
 * @returns {Object} 包含工作簿和工作表的对象
 */
function createCostCalculationWorkbook() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("3-分部分项成本测算");

  // 设置表头 - 只设置A~H列
  worksheet.columns = [
    { header: "序号", key: "serialNo", width: 8 }, // A列
    { header: "分部", key: "division", width: 15 }, // B列
    { header: "目标", key: "target", width: 30 }, // C列
    { header: "项目特征", key: "feature", width: 40 }, // D列
    { header: "计量单位", key: "unit", width: 12 }, // E列
    { header: "工程量", key: "quantity", width: 15 }, // F列
    { header: "含量", key: "content", width: 12 }, // G列
    { header: "测算成本量", key: "costAmount", width: 15 }, // H列
  ];

  // 设置表头样式
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  return { workbook, worksheet };
}

/**
 * 添加数据行到工作表
 * @param {Object} worksheet - Excel工作表对象
 * @param {Array} processedData - 处理后的数据数组
 */
function addDataRows(worksheet, processedData) {
  let rowIndex = 2; // 从第2行开始（第1行是表头）
  let parentRowIndex = null; // 记录父级行的行号

  processedData.forEach((item, index) => {
    // 添加数据行 - 只添加A~H列
    const row = worksheet.addRow({
      serialNo: item.序号,
      division: item.分部,
      target: item.目标,
      feature: item.项目特征,
      unit: item.计量单位,
      quantity: item.工程量,
      content: item.含量,
      costAmount: "", // H列暂时为空，后面添加公式
    });

    // 根据数据级别处理公式
    if (item.数据级别 === 2) {
      // 2级数据（目标）- 记录为父级行
      parentRowIndex = rowIndex;
    } else if (["人", "材", "专"].includes(item.分部) && parentRowIndex) {
      // 3级数据（人、材、专）- 使用父级行的工程量 × 当前行的含量
      const parentFColumn = `F${parentRowIndex}`; // 父级行的工程量（F列）
      const currentGColumn = `G${rowIndex}`; // 当前行的含量（G列）
      const formula = `=${parentFColumn}*${currentGColumn}`;

      row.getCell(8).value = { formula: formula }; // H列

      console.log(
        `  \x1b[36m[公式] \x1b[0m第${rowIndex}行 H列: \x1b[33m${formula}\x1b[0m (${item.分部}: ${item.目标}) - 父级第${parentRowIndex}行`
      );
    }

    // 设置数据行样式
    setRowStyles(worksheet, rowIndex, item);

    rowIndex++;
  });

  return rowIndex - 1; // 返回总行数
}

/**
 * 设置行样式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} rowIndex - 行索引
 * @param {Object} item - 数据项
 */
function setRowStyles(worksheet, rowIndex, item) {
  const row = worksheet.getRow(rowIndex);

  // 根据数据级别设置不同样式
  switch (item.数据级别) {
    case 1: // 分部级别
      row.font = { bold: true };
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFD0D0D0" }, // 深灰色
      };
      break;
    case 2: // 目标级别
      row.font = { bold: false };
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF0F0F0" }, // 浅灰色
      };
      break;
    case 3: // 人/材/专级别
      row.font = { bold: false };
      // 不设置背景色，保持默认白色
      break;
  }

  // 设置数字格式
  setNumberFormats(row, item);
}

/**
 * 设置数字格式
 * @param {Object} row - Excel行对象
 * @param {Object} item - 数据项
 */
function setNumberFormats(row, item) {
  // F列：工程量 - 数字格式
  if (item.工程量 && !isNaN(parseFloat(item.工程量))) {
    row.getCell(6).numFmt = "0.00";
  }

  // G列：含量 - 数字格式
  if (item.含量 && !isNaN(parseFloat(item.含量))) {
    row.getCell(7).numFmt = "0.00";
  }

  // H列：测算成本量 - 数字格式
  row.getCell(8).numFmt = "0.00";
}

/**
 * 设置单元格边框和对齐方式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} rowCount - 总行数
 */
function setCellStyles(worksheet, rowCount) {
  // 设置所有单元格的边框和对齐方式
  for (let i = 1; i <= rowCount; i++) {
    for (let j = 1; j <= 8; j++) {
      // A到H列
      const cell = worksheet.getCell(i, j);

      // 设置边框
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // 设置对齐方式
      cell.alignment = {
        vertical: "middle",
        wrapText: true,
      };

      // 数字列居中对齐
      if (j >= 5 && j <= 8) {
        // E到H列（计量单位、工程量、含量、测算成本量）
        cell.alignment.horizontal = "center";
      }
    }
  }
}

/**
 * 生成 Excel 文件
 * @param {Array} processedData - 处理后的数据数组
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 文件名（不含扩展名）
 * @returns {Promise<string>} 生成的Excel文件路径
 */
async function generateCostCalculationExcel(
  processedData,
  outputDir,
  fileName
) {
  console.log(
    `\n\x1b[36m[生成] \x1b[0m开始生成 Excel 文件: \x1b[33m${fileName}.xlsx\x1b[0m`
  );

  // 创建工作簿和工作表
  const { workbook, worksheet } = createCostCalculationWorkbook();

  // 添加数据行
  const rowCount = addDataRows(worksheet, processedData);

  // 设置单元格样式
  setCellStyles(worksheet, rowCount);

  // 生成输出文件路径
  const outputFilePath = path.join(outputDir, `${fileName}.xlsx`);

  try {
    // 保存Excel文件
    await workbook.xlsx.writeFile(outputFilePath);

    console.log(
      `\x1b[32m[成功] \x1b[0m Excel文件已保存: \x1b[33m${outputFilePath}\x1b[0m`
    );
    console.log(
      `\x1b[36m[信息] \x1b[0m总行数: \x1b[32m${rowCount}\x1b[0m (包含表头)`
    );

    return outputFilePath;
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m生成Excel文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 创建材料清单工作簿
 * @returns {Object} 包含工作簿和工作表的对象
 */
function createMaterialListWorkbook() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("4-材料清单");

  // 设置表头 - A~L列
  worksheet.columns = [
    { header: "", key: "empty", width: 8 }, // A列 - 空列
    { header: "分部工程", key: "division", width: 15 }, // B列
    { header: "原测算清单", key: "originalList", width: 30 }, // C列
    { header: "规格型号", key: "specification", width: 25 }, // D列
    { header: "计量单位", key: "unit", width: 12 }, // E列
    { header: "测算含量", key: "content", width: 12 }, // F列
    { header: "测算成本量", key: "costAmount", width: 15 }, // G列
    { header: "测算单价", key: "unitPrice", width: 12 }, // H列
    { header: "测算金额", key: "totalAmount", width: 15 }, // I列
    { header: "税率（%）", key: "taxRate", width: 12 }, // J列
    { header: "测算说明", key: "description", width: 20 }, // K列
    { header: "标签1", key: "tag1", width: 15 }, // L列
  ];

  // 设置表头样式
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  return { workbook, worksheet };
}

/**
 * 商品混凝土单价计算规则
 */
const concreteRules = {
  // 基准价格档次（相对于C30）
  priceAdjustment: {
    C15: -20, // C15比C30减20（C20/C25各减10，共减20）
    C20: -10, // C20比C30减10
    C25: -10, // C25比C30减10
    C30: 0, // C30为基准
    C35: 15, // C35比C30加15
    C40: 15, // C40比C30加15
    C45: 25, // C45比C40加25，即比C30加40，但这里应该是相对C40的增量
    C50: 25, // C50比C40加25，即比C30加40，但这里应该是相对C40的增量
    C55: 30, // C55比C50加30，即比C30加70，但这里应该是相对C50的增量
  },

  // 附加费字典
  additionalFees: {
    微膨胀: 25,
    补偿收缩: 20,
    P8: 15,
    细石: 10,
  },

  // 不含泵送费用（C15/C20/C25需要减去）
  pumpingFee: 12,
};

/**
 * 解析混凝土标号和附加费
 * @param {string} concreteName - 混凝土名称
 * @returns {Object} 解析结果
 */
function parseConcreteName(concreteName) {
  const result = {
    grade: "",
    additionalFees: [],
    isPumping: true, // 默认含泵送
  };

  // 提取混凝土标号
  const gradeMatch = concreteName.match(/C(\d+)/);
  if (gradeMatch) {
    result.grade = `C${gradeMatch[1]}`;
  }

  // 检查附加费
  Object.keys(concreteRules.additionalFees).forEach((fee) => {
    if (concreteName.includes(fee)) {
      result.additionalFees.push(fee);
    }
  });

  // C15/C20/C25不含泵送
  if (["C15", "C20", "C25"].includes(result.grade)) {
    result.isPumping = false;
  }

  return result;
}

/**
 * 生成商品混凝土单价公式
 * @param {string} concreteName - 混凝土名称
 * @param {number} parentRowIndex - 父级行索引
 * @returns {string} Excel公式
 */
function generateConcreteUnitPriceFormula(concreteName, parentRowIndex) {
  const parsed = parseConcreteName(concreteName);

  if (!parsed.grade) {
    return ""; // 无法解析标号，返回空
  }

  // 计算总的价格调整
  let totalAdjustment = 0;

  // 1. 基础价格调整（相对于C30）
  if (parsed.grade === "C15") {
    totalAdjustment += -20; // C15比C30减20
  } else if (parsed.grade === "C20") {
    totalAdjustment += -10; // C20比C30减10
  } else if (parsed.grade === "C25") {
    totalAdjustment += -10; // C25比C30减10
  } else if (parsed.grade === "C30") {
    totalAdjustment += 0; // C30为基准
  } else if (parsed.grade === "C35") {
    totalAdjustment += 15; // C35比C30加15
  } else if (parsed.grade === "C40") {
    totalAdjustment += 15; // C40比C30加15
  } else if (parsed.grade === "C45") {
    totalAdjustment += 40; // C45比C40加25，即比C30加40
  } else if (parsed.grade === "C50") {
    totalAdjustment += 40; // C50比C40加25，即比C30加40
  } else if (parsed.grade === "C55") {
    totalAdjustment += 70; // C55比C50加30，即比C30加70
  }

  // 2. 附加费
  parsed.additionalFees.forEach((fee) => {
    totalAdjustment += concreteRules.additionalFees[fee] || 0;
  });

  // 3. 泵送费调整（C15/C20/C25不含泵送，需要减去泵送费）
  if (!parsed.isPumping) {
    totalAdjustment -= concreteRules.pumpingFee;
  }

  // 构建公式
  const parentCell = `H${parentRowIndex}`; // 父级单价
  let formula = `=${parentCell}`;

  if (totalAdjustment !== 0) {
    formula +=
      totalAdjustment > 0 ? `+${totalAdjustment}` : `${totalAdjustment}`;
  }

  return formula;
}

module.exports = {
  createCostCalculationWorkbook,
  addDataRows,
  setCellStyles,
  generateCostCalculationExcel,
  createMaterialListWorkbook,
  parseConcreteName,
  generateConcreteUnitPriceFormula,
  concreteRules,
};
