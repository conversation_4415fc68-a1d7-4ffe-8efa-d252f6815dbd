const fs = require("fs");
const path = require("path");
const ExcelJS = require("exceljs");
const {
  createMaterialListWorkbook,
  generateConcreteUnitPriceFormula,
} = require("./excel-writer");

/**
 * 从JSON文件读取分部分项成本测算数据
 * @param {string} jsonFilePath - JSON文件路径
 * @returns {Array} 数据数组
 */
function readCostCalculationJson(jsonFilePath) {
  console.log(
    `\n\x1b[36m[读取] \x1b[0m读取JSON文件: \x1b[33m${path.basename(
      jsonFilePath
    )}\x1b[0m`
  );

  if (!fs.existsSync(jsonFilePath)) {
    throw new Error(`JSON文件不存在: ${jsonFilePath}`);
  }

  const jsonContent = fs.readFileSync(jsonFilePath, "utf8");
  const data = JSON.parse(jsonContent);

  console.log(
    `\x1b[36m[信息] \x1b[0m读取到 \x1b[32m${data.length}\x1b[0m 条数据`
  );

  return data;
}

/**
 * 筛选分部为"材"的数据
 * @param {Array} data - 原始数据
 * @returns {Array} 筛选后的材料数据
 */
function filterMaterialData(data) {
  console.log(`\n\x1b[36m[筛选] \x1b[0m筛选分部为"材"的数据...`);

  const materialData = data.filter(
    (item) => item.分部 === "材" && item.数据级别 === 3
  );

  console.log(
    `\x1b[36m[信息] \x1b[0m筛选到 \x1b[32m${materialData.length}\x1b[0m 条材料数据`
  );

  // 计算测算成本量：需要找到对应的父级目标行的工程量
  const parentQuantityMap = new Map();

  // 先收集所有2级数据（目标）的工程量
  data
    .filter((item) => item.数据级别 === 2)
    .forEach((item) => {
      const key = `${item.父级分部}|${item.目标}`;
      parentQuantityMap.set(key, parseFloat(item.工程量) || 0);
    });

  // 为每个材料数据计算测算成本量
  materialData.forEach((item) => {
    const parentKey = `${item.父级分部}|${item.父级目标}`;
    const parentQuantity = parentQuantityMap.get(parentKey) || 0;
    const content = parseFloat(item.含量) || 0;

    // 测算成本量 = 父级工程量 × 当前含量
    item.测算成本量 = parentQuantity * content;
  });

  // 统计材料类型分布
  const materialTypeStats = {};
  materialData.forEach((item) => {
    const type = item.材料类型 || "其他";
    materialTypeStats[type] = (materialTypeStats[type] || 0) + 1;
  });

  console.log(`\x1b[36m[统计] \x1b[0m材料类型分布:`);
  Object.entries(materialTypeStats).forEach(([type, count]) => {
    console.log(`  \x1b[33m${type}: ${count} 项\x1b[0m`);
  });

  return materialData;
}

/**
 * 按材料类型和名称+单位分组数据
 * @param {Array} materialData - 材料数据
 * @returns {Object} 分组后的数据
 */
function groupMaterialData(materialData) {
  console.log(`\n\x1b[36m[分组] \x1b[0m按材料类型和名称+单位分组数据...`);

  const grouped = {
    钢筋: [],
    商品混凝土: [],
    砂浆: [],
    砖: [],
    其他: {},
  };

  materialData.forEach((item) => {
    const materialType = item.材料类型 || "其他";

    if (materialType === "钢筋") {
      grouped.钢筋.push(item);
    } else if (materialType === "商品混凝土") {
      grouped.商品混凝土.push(item);
    } else if (materialType === "砂浆") {
      grouped.砂浆.push(item);
    } else if (materialType === "砖") {
      grouped.砖.push(item);
    } else {
      // 其他材料按名称+单位分组
      const groupKey = `${item.目标}|${item.计量单位}`;
      if (!grouped.其他[groupKey]) {
        grouped.其他[groupKey] = [];
      }
      grouped.其他[groupKey].push(item);
    }
  });

  console.log(`\x1b[36m[分组结果] \x1b[0m`);
  console.log(`  \x1b[33m钢筋: ${grouped.钢筋.length} 项\x1b[0m`);
  console.log(`  \x1b[33m商品混凝土: ${grouped.商品混凝土.length} 项\x1b[0m`);
  console.log(`  \x1b[33m砂浆: ${grouped.砂浆.length} 项\x1b[0m`);
  console.log(`  \x1b[33m砖: ${grouped.砖.length} 项\x1b[0m`);
  console.log(
    `  \x1b[33m其他分组: ${Object.keys(grouped.其他).length} 组\x1b[0m`
  );

  return grouped;
}

/**
 * 创建商品混凝土父级行
 * @param {Array} concreteItems - 商品混凝土项目数组
 * @returns {Object} 父级行数据
 */
function createConcreteParentRow(concreteItems) {
  // 计算总的测算成本量
  const totalCostAmount = concreteItems.reduce((sum, item) => {
    return sum + (item.测算成本量 || 0);
  }, 0);

  return {
    type: "parent",
    分部工程: "商品混凝土",
    原测算清单: "商品混凝土",
    规格型号: "",
    计量单位: "m³",
    测算含量: "",
    测算成本量: totalCostAmount,
    测算单价: "", // 用户填写
    测算金额: `=G{ROW}*H{ROW}`, // 公式模板
    税率: "",
    测算说明: "",
    标签1: "",
    children: concreteItems,
  };
}

/**
 * 创建其他材料的父级行
 * @param {string} groupKey - 分组键（名称|单位）
 * @param {Array} items - 项目数组
 * @returns {Object} 父级行数据
 */
function createOtherMaterialParentRow(groupKey, items) {
  const [name, unit] = groupKey.split("|");

  // 计算总的测算成本量
  const totalCostAmount = items.reduce((sum, item) => {
    return sum + (item.测算成本量 || 0);
  }, 0);

  return {
    type: "parent",
    分部工程: name,
    原测算清单: name,
    规格型号: "",
    计量单位: unit,
    测算含量: "",
    测算成本量: totalCostAmount,
    测算单价: "", // 用户填写
    测算金额: `=G{ROW}*H{ROW}`, // 公式模板
    税率: "",
    测算说明: "",
    标签1: "",
    children: items,
  };
}

/**
 * 生成材料清单行数据
 * @param {Object} groupedData - 分组后的数据
 * @returns {Array} 行数据数组
 */
function generateMaterialListRows(groupedData) {
  console.log(`\n\x1b[36m[生成] \x1b[0m生成材料清单行数据...`);

  const rows = [];

  // 1. 钢筋类型数据
  if (groupedData.钢筋.length > 0) {
    console.log(
      `  \x1b[36m[钢筋] \x1b[0m添加 ${groupedData.钢筋.length} 项钢筋数据`
    );
    groupedData.钢筋.forEach((item) => {
      rows.push({
        type: "material",
        分部工程: item.父级分部,
        原测算清单: item.父级目标,
        规格型号: item.目标,
        计量单位: item.计量单位,
        测算含量: item.含量,
        测算成本量: item.测算成本量, // G列值：父级工程量×当前含量
        测算单价: "", // 用户填写
        测算金额: `=G{ROW}*H{ROW}`, // 公式模板：测算成本量*测算单价
        税率: "",
        测算说明: "",
        标签1: "",
        原始数据: item,
      });
    });
  }

  // 2. 商品混凝土类型数据
  if (groupedData.商品混凝土.length > 0) {
    console.log(
      `  \x1b[36m[商品混凝土] \x1b[0m添加商品混凝土父级行和 ${groupedData.商品混凝土.length} 项子级数据`
    );

    // 创建父级行
    const parentRow = createConcreteParentRow(groupedData.商品混凝土);
    rows.push(parentRow);

    // 添加子级行
    groupedData.商品混凝土.forEach((item) => {
      rows.push({
        type: "child",
        分部工程: item.父级分部,
        原测算清单: item.父级目标,
        规格型号: item.目标,
        计量单位: item.计量单位,
        测算含量: item.含量,
        测算成本量: item.测算成本量, // G列值：父级工程量×当前含量
        测算单价: "", // 将使用公式
        测算金额: `=G{ROW}*H{ROW}`, // 公式模板：测算成本量*测算单价
        税率: `={PARENT_TAX_RATE}`, // 引用父级税率
        测算说明: "",
        标签1: "",
        原始数据: item,
        parentType: "商品混凝土",
      });
    });
  }

  // 3. 砂浆类型数据
  if (groupedData.砂浆.length > 0) {
    console.log(`  \x1b[36m[砂浆] \x1b[0m按分组添加砂浆数据`);
    const mortarGroups = {};
    groupedData.砂浆.forEach((item) => {
      const groupKey = `${item.目标}|${item.计量单位}`;
      if (!mortarGroups[groupKey]) {
        mortarGroups[groupKey] = [];
      }
      mortarGroups[groupKey].push(item);
    });

    Object.entries(mortarGroups).forEach(([groupKey, items]) => {
      if (items.length > 1) {
        // 多个项目，创建父级行
        const parentRow = createOtherMaterialParentRow(groupKey, items);
        rows.push(parentRow);

        items.forEach((item) => {
          rows.push({
            type: "child",
            分部工程: item.父级分部,
            原测算清单: item.父级目标,
            规格型号: item.目标,
            计量单位: item.计量单位,
            测算含量: item.含量,
            测算成本量: item.测算成本量,
            测算单价: "",
            测算金额: `=G{ROW}*H{ROW}`,
            税率: `={PARENT_TAX_RATE}`,
            测算说明: "",
            标签1: "",
            原始数据: item,
            parentType: "砂浆",
          });
        });
      } else {
        // 单个项目，直接添加
        const item = items[0];
        rows.push({
          type: "material",
          分部工程: item.父级分部,
          原测算清单: item.父级目标,
          规格型号: item.目标,
          计量单位: item.计量单位,
          测算含量: item.含量,
          测算成本量: item.测算成本量,
          测算单价: "",
          测算金额: `=G{ROW}*H{ROW}`,
          税率: "",
          测算说明: "",
          标签1: "",
          原始数据: item,
        });
      }
    });
  }

  // 4. 砖类型数据
  if (groupedData.砖.length > 0) {
    console.log(`  \x1b[36m[砖] \x1b[0m按分组添加砖数据`);
    const brickGroups = {};
    groupedData.砖.forEach((item) => {
      const groupKey = `${item.目标}|${item.计量单位}`;
      if (!brickGroups[groupKey]) {
        brickGroups[groupKey] = [];
      }
      brickGroups[groupKey].push(item);
    });

    Object.entries(brickGroups).forEach(([groupKey, items]) => {
      if (items.length > 1) {
        // 多个项目，创建父级行
        const parentRow = createOtherMaterialParentRow(groupKey, items);
        rows.push(parentRow);

        items.forEach((item) => {
          rows.push({
            type: "child",
            分部工程: item.父级分部,
            原测算清单: item.父级目标,
            规格型号: item.目标,
            计量单位: item.计量单位,
            测算含量: item.含量,
            测算成本量: item.测算成本量,
            测算单价: "",
            测算金额: `=G{ROW}*H{ROW}`,
            税率: `={PARENT_TAX_RATE}`,
            测算说明: "",
            标签1: "",
            原始数据: item,
            parentType: "砖",
          });
        });
      } else {
        // 单个项目，直接添加
        const item = items[0];
        rows.push({
          type: "material",
          分部工程: item.父级分部,
          原测算清单: item.父级目标,
          规格型号: item.目标,
          计量单位: item.计量单位,
          测算含量: item.含量,
          测算成本量: item.测算成本量,
          测算单价: "",
          测算金额: `=G{ROW}*H{ROW}`,
          税率: "",
          测算说明: "",
          标签1: "",
          原始数据: item,
        });
      }
    });
  }

  // 5. 其他类型数据
  if (Object.keys(groupedData.其他).length > 0) {
    console.log(`  \x1b[36m[其他] \x1b[0m按原始数据顺序添加其他材料数据`);

    // 按原始行号排序
    const otherItems = [];
    Object.values(groupedData.其他).forEach((items) => {
      otherItems.push(...items);
    });
    otherItems.sort((a, b) => a.原始行号 - b.原始行号);

    const otherGroups = {};
    otherItems.forEach((item) => {
      const groupKey = `${item.目标}|${item.计量单位}`;
      if (!otherGroups[groupKey]) {
        otherGroups[groupKey] = [];
      }
      otherGroups[groupKey].push(item);
    });

    Object.entries(otherGroups).forEach(([groupKey, items]) => {
      if (items.length > 1) {
        // 多个项目，创建父级行
        const parentRow = createOtherMaterialParentRow(groupKey, items);
        rows.push(parentRow);

        items.forEach((item) => {
          rows.push({
            type: "child",
            分部工程: item.父级分部,
            原测算清单: item.父级目标,
            规格型号: item.目标,
            计量单位: item.计量单位,
            测算含量: item.含量,
            测算成本量: item.测算成本量,
            测算单价: "",
            测算金额: `=G{ROW}*H{ROW}`,
            税率: `={PARENT_TAX_RATE}`,
            测算说明: "",
            标签1: "",
            原始数据: item,
            parentType: "其他",
          });
        });
      } else {
        // 单个项目，直接添加
        const item = items[0];
        rows.push({
          type: "material",
          分部工程: item.父级分部,
          原测算清单: item.父级目标,
          规格型号: item.目标,
          计量单位: item.计量单位,
          测算含量: item.含量,
          测算成本量: item.测算成本量,
          测算单价: "",
          测算金额: `=G{ROW}*H{ROW}`,
          税率: "",
          测算说明: "",
          标签1: "",
          原始数据: item,
        });
      }
    });
  }

  console.log(
    `\x1b[36m[完成] \x1b[0m生成了 \x1b[32m${rows.length}\x1b[0m 行数据`
  );

  return rows;
}

/**
 * 添加数据行到工作表
 * @param {Object} worksheet - Excel工作表对象
 * @param {Array} rows - 行数据数组
 */
function addMaterialListRows(worksheet, rows) {
  console.log(`\n\x1b[36m[写入] \x1b[0m开始写入材料清单数据到Excel...`);

  let rowIndex = 2; // 从第2行开始（第1行是表头）
  const parentRowIndexMap = new Map(); // 记录父级行的索引

  rows.forEach((rowData, index) => {
    // 添加数据行
    const row = worksheet.addRow({
      empty: "", // A列 - 空列
      division: rowData.分部工程, // B列
      originalList: rowData.原测算清单, // C列
      specification: rowData.规格型号, // D列
      unit: rowData.计量单位, // E列
      content: rowData.测算含量, // F列
      costAmount: rowData.测算成本量, // G列
      unitPrice: "", // H列 - 测算单价，暂时为空
      totalAmount: "", // I列 - 测算金额，后面添加公式
      taxRate: "", // J列 - 税率，后面处理
      description: rowData.测算说明, // K列
      tag1: rowData.标签1, // L列
    });

    // 处理不同类型的行
    if (rowData.type === "parent") {
      // 父级行
      parentRowIndexMap.set(rowData.分部工程, rowIndex);

      // 设置测算金额公式（父级行通过公式合计子级）
      if (rowData.children && rowData.children.length > 0) {
        // 计算子级行的范围
        const childStartRow = rowIndex + 1;
        const childEndRow = rowIndex + rowData.children.length;
        row.getCell(9).value = {
          formula: `=SUM(I${childStartRow}:I${childEndRow})`,
        }; // I列
        row.getCell(7).value = {
          formula: `=SUM(G${childStartRow}:G${childEndRow})`,
        }; // G列
      }

      // 设置父级行样式
      row.font = { bold: true };
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFE0E0E0" },
      };

      console.log(
        `    \x1b[36m[父级] \x1b[0m第${rowIndex}行: ${rowData.分部工程}`
      );
    } else if (rowData.type === "child") {
      // 子级行

      // 设置测算金额公式
      row.getCell(9).value = {
        formula: rowData.测算金额.replace("{ROW}", rowIndex),
      }; // I列

      // 处理税率公式（引用父级）
      if (rowData.税率 && rowData.税率.includes("{PARENT_TAX_RATE}")) {
        const parentRowIndex = parentRowIndexMap.get(rowData.parentType);
        if (parentRowIndex) {
          row.getCell(10).value = { formula: `=J${parentRowIndex}` }; // J列
        }
      }

      // 处理商品混凝土的单价公式
      if (rowData.parentType === "商品混凝土" && rowData.原始数据) {
        const parentRowIndex = parentRowIndexMap.get("商品混凝土");
        if (parentRowIndex) {
          const formula = generateConcreteUnitPriceFormula(
            rowData.原始数据.目标,
            parentRowIndex
          );
          if (formula) {
            row.getCell(8).value = { formula: formula }; // H列
            console.log(
              `      \x1b[35m[公式] \x1b[0m第${rowIndex}行 H列: \x1b[33m${formula}\x1b[0m (${rowData.原始数据.目标})`
            );
          }
        }
      }

      // 设置子级行样式（缩进）
      row.getCell(2).alignment = { indent: 1 }; // B列缩进
    } else {
      // 普通材料行

      // 设置测算金额公式
      row.getCell(9).value = {
        formula: rowData.测算金额.replace("{ROW}", rowIndex),
      }; // I列
    }

    rowIndex++;
  });

  console.log(
    `\x1b[32m[完成] \x1b[0m写入了 \x1b[32m${rows.length}\x1b[0m 行数据`
  );

  return rowIndex - 1; // 返回总行数
}

/**
 * 设置单元格样式和格式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} totalRows - 总行数
 */
function setMaterialListCellStyles(worksheet, totalRows) {
  console.log(`\n\x1b[36m[样式] \x1b[0m设置单元格样式和格式...`);

  // 设置数字格式
  for (let rowNum = 2; rowNum <= totalRows; rowNum++) {
    const row = worksheet.getRow(rowNum);

    // F列（测算含量）- 数字格式，保留4位小数
    row.getCell(6).numFmt = "0.0000";

    // G列（测算成本量）- 数字格式，保留2位小数
    row.getCell(7).numFmt = "0.00";

    // H列（测算单价）- 数字格式，保留2位小数
    row.getCell(8).numFmt = "0.00";

    // I列（测算金额）- 数字格式，保留2位小数
    row.getCell(9).numFmt = "0.00";

    // J列（税率）- 百分比格式，保留2位小数
    row.getCell(10).numFmt = "0.00%";
  }

  // 设置列宽
  worksheet.getColumn(1).width = 8; // A列
  worksheet.getColumn(2).width = 15; // B列
  worksheet.getColumn(3).width = 30; // C列
  worksheet.getColumn(4).width = 25; // D列
  worksheet.getColumn(5).width = 12; // E列
  worksheet.getColumn(6).width = 12; // F列
  worksheet.getColumn(7).width = 15; // G列
  worksheet.getColumn(8).width = 12; // H列
  worksheet.getColumn(9).width = 15; // I列
  worksheet.getColumn(10).width = 12; // J列
  worksheet.getColumn(11).width = 20; // K列
  worksheet.getColumn(12).width = 15; // L列

  console.log(`\x1b[32m[完成] \x1b[0m样式设置完成`);
}

/**
 * 查找原始Excel文件
 * @param {string} inputDir - 输入目录
 * @returns {string|null} 找到的Excel文件路径
 */
function findOriginalExcelFile(inputDir) {
  console.log(`\n\x1b[36m[查找] \x1b[0m在 ${inputDir} 中查找原始Excel文件...`);

  if (!fs.existsSync(inputDir)) {
    console.log(`\x1b[31m[错误] \x1b[0m输入目录不存在: ${inputDir}`);
    return null;
  }

  const files = fs.readdirSync(inputDir);
  const excelFiles = files.filter(
    (file) => file.toLowerCase().endsWith(".xlsx") && !file.startsWith("~$")
  );

  if (excelFiles.length === 0) {
    console.log(`\x1b[31m[错误] \x1b[0m在 ${inputDir} 中没有找到Excel文件`);
    return null;
  }

  // 优先选择包含"分部分项成本测算"的文件
  const targetFile =
    excelFiles.find((file) => file.includes("分部分项成本测算")) ||
    excelFiles[0];
  const filePath = path.join(inputDir, targetFile);

  console.log(
    `\x1b[36m[找到] \x1b[0m使用Excel文件: \x1b[33m${targetFile}\x1b[0m`
  );

  return filePath;
}

/**
 * 生成材料清单Excel文件（修改原有文件）
 * @param {string} jsonFilePath - JSON文件路径
 * @param {string} inputDir - 输入目录（查找原始Excel文件）
 * @param {string} outputDir - 输出目录
 * @param {string} fileName - 文件名（不含扩展名）
 * @returns {string} 生成的Excel文件路径
 */
async function generateMaterialListExcel(
  jsonFilePath,
  inputDir,
  outputDir,
  fileName
) {
  console.log(`\n\x1b[36m[开始] \x1b[0m生成材料清单Excel文件...`);

  try {
    // 1. 查找原始Excel文件
    const originalExcelPath = findOriginalExcelFile(inputDir);
    if (!originalExcelPath) {
      throw new Error("未找到原始Excel文件");
    }

    // 2. 读取JSON数据
    const data = readCostCalculationJson(jsonFilePath);

    // 3. 筛选材料数据
    const materialData = filterMaterialData(data);

    if (materialData.length === 0) {
      console.log(`\x1b[31m[警告] \x1b[0m没有找到材料数据`);
      return null;
    }

    // 4. 分组数据
    const groupedData = groupMaterialData(materialData);

    // 5. 生成行数据
    const rows = generateMaterialListRows(groupedData);

    // 6. 读取原始Excel文件
    console.log(`\x1b[36m[读取] \x1b[0m读取原始Excel文件...`);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(originalExcelPath);

    // 7. 检查是否已存在"4-材料清单"工作表，如果存在则删除
    const existingWorksheet = workbook.getWorksheet("4-材料清单");
    if (existingWorksheet) {
      console.log(`\x1b[36m[删除] \x1b[0m删除已存在的"4-材料清单"工作表...`);
      workbook.removeWorksheet(existingWorksheet.id);
    }

    // 8. 创建新的"4-材料清单"工作表
    console.log(`\x1b[36m[创建] \x1b[0m创建"4-材料清单"工作表...`);
    const worksheet = workbook.addWorksheet("4-材料清单");

    // 设置表头 - A~L列
    worksheet.columns = [
      { header: "", key: "empty", width: 8 }, // A列 - 空列
      { header: "分部工程", key: "division", width: 15 }, // B列
      { header: "原测算清单", key: "originalList", width: 30 }, // C列
      { header: "规格型号", key: "specification", width: 25 }, // D列
      { header: "计量单位", key: "unit", width: 12 }, // E列
      { header: "测算含量", key: "content", width: 12 }, // F列
      { header: "测算成本量", key: "costAmount", width: 15 }, // G列
      { header: "测算单价", key: "unitPrice", width: 12 }, // H列
      { header: "测算金额", key: "totalAmount", width: 15 }, // I列
      { header: "税率（%）", key: "taxRate", width: 12 }, // J列
      { header: "测算说明", key: "description", width: 20 }, // K列
      { header: "标签1", key: "tag1", width: 15 }, // L列
    ];

    // 设置表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // 9. 添加数据行
    const totalRows = addMaterialListRows(worksheet, rows);

    // 10. 设置样式
    setMaterialListCellStyles(worksheet, totalRows);

    // 11. 生成输出文件路径
    const outputFilePath = path.join(outputDir, `${fileName}.xlsx`);

    // 12. 保存Excel文件
    await workbook.xlsx.writeFile(outputFilePath);

    console.log(
      `\x1b[32m[成功] \x1b[0m材料清单Excel文件已保存: \x1b[33m${outputFilePath}\x1b[0m`
    );
    console.log(
      `\x1b[36m[信息] \x1b[0m总行数: \x1b[32m${totalRows}\x1b[0m (包含表头)`
    );
    console.log(
      `\x1b[36m[信息] \x1b[0m数据行数: \x1b[32m${rows.length}\x1b[0m`
    );

    return outputFilePath;
  } catch (error) {
    console.error(
      `\x1b[31m[错误] \x1b[0m生成材料清单Excel文件失败: ${error.message}`
    );
    throw error;
  }
}

module.exports = {
  readCostCalculationJson,
  filterMaterialData,
  groupMaterialData,
  generateMaterialListRows,
  createConcreteParentRow,
  createOtherMaterialParentRow,
  addMaterialListRows,
  setMaterialListCellStyles,
  findOriginalExcelFile,
  generateMaterialListExcel,
};
