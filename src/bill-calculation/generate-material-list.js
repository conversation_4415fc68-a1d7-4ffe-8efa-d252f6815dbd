const path = require("path");
const { generateMaterialListExcel } = require("./material-list-generator");
const { inputDir, outputDir, configureConsole } = require("./config");

/**
 * 主函数 - 生成材料清单
 */
async function generateMaterialList() {
  // 配置控制台输出
  configureConsole();

  console.log("\n\x1b[36m=".repeat(60) + "\x1b[0m");
  console.log("\x1b[36m           材料清单生成工具\x1b[0m");
  console.log("\x1b[36m=".repeat(60) + "\x1b[0m");

  try {
    console.log("\n\x1b[36m[开始] \x1b[33m生成4-材料清单...\x1b[0m");

    // JSON文件路径
    const jsonFilePath = path.join(
      __dirname,
      "../../json/bill-calculation/分部分项成本测算.json"
    );

    // 检查JSON文件是否存在
    const fs = require("fs");
    if (!fs.existsSync(jsonFilePath)) {
      console.error(`\x1b[31m[错误] \x1b[0mJSON文件不存在: ${jsonFilePath}`);
      console.log(
        `\x1b[33m[提示] \x1b[0m请先运行分部分项成本测算功能生成JSON文件。`
      );
      console.log("\x1b[36m[退出] \x1b[0m按回车键退出...");
      process.stdin.once("data", () => {
        process.exit(1);
      });
      return;
    }

    // 生成材料清单Excel文件
    const outputFilePath = await generateMaterialListExcel(
      jsonFilePath,
      inputDir,
      outputDir,
      "分部分项成本测算"
    );

    if (outputFilePath) {
      console.log(`\n\x1b[32m[完成] \x1b[0m材料清单生成完成！`);
      console.log(`\x1b[32m文件已保存在: ${outputFilePath}\x1b[0m`);
    } else {
      console.log(`\n\x1b[31m[失败] \x1b[0m材料清单生成失败`);
    }
  } catch (error) {
    console.error(
      `\x1b[31m[错误] \x1b[0m生成材料清单时发生错误: ${error.message}`
    );
    console.error(error.stack);
  }

  console.log("\n\x1b[36m[退出] \x1b[0m按回车键退出...");
  process.stdin.once("data", () => {
    process.exit(0);
  });
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  generateMaterialList();
}

module.exports = {
  generateMaterialList,
};
