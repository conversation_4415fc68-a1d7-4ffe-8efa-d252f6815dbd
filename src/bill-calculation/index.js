const fs = require("fs");
const path = require("path");
const {
  inputDir,
  jsonBaseDir,
  outputDir,
  ensureDirectories,
  showWelcomeMessage,
  configureConsole,
} = require("./config");
const { getExcelFiles, readCostCalculationData } = require("./excel-reader");
const { processRawData, validateProcessedData } = require("./data-processor");
const { writeAllFiles } = require("./json-writer");
const { generateCostCalculationExcel } = require("./excel-writer");
const { generateMaterialListExcel } = require("./material-list-generator");

/**
 * 处理单个Excel文件
 * @param {string} excelFile - Excel文件名
 * @returns {Promise<Object>} 处理结果
 */
async function processExcelFile(excelFile) {
  const inputExcelFilePath = path.join(inputDir, excelFile);
  const fileName = path.basename(excelFile, path.extname(excelFile));

  console.log(`\n\x1b[36m[开始] \x1b[0m处理文件: \x1b[33m${excelFile}\x1b[0m`);

  try {
    // 1. 读取Excel文件中的原始数据
    const rawData = await readCostCalculationData(inputExcelFilePath);

    if (rawData.length === 0) {
      console.log(`\x1b[31m[警告] \x1b[0m文件 ${excelFile} 中没有找到有效数据`);
      return { success: false, message: "没有找到有效数据" };
    }

    // 2. 处理原始数据，生成平级结构
    const processedData = processRawData(rawData);

    // 3. 验证处理后的数据
    const isValid = validateProcessedData(processedData);
    if (!isValid) {
      throw new Error("数据验证失败");
    }

    // 4. 写入JSON文件
    const { jsonFilePath, summaryFilePath } = writeAllFiles(
      processedData,
      jsonBaseDir,
      fileName
    );

    // 5. 生成Excel文件（带公式）
    const excelFilePath = await generateCostCalculationExcel(
      processedData,
      outputDir,
      fileName
    );

    console.log(`\x1b[32m[完成] \x1b[0m文件 ${excelFile} 处理完成`);

    return {
      success: true,
      fileName,
      jsonFilePath,
      summaryFilePath,
      excelFilePath,
      dataCount: processedData.length,
    };
  } catch (error) {
    console.error(
      `\x1b[31m[错误] \x1b[0m处理文件 ${excelFile} 时出错: ${error.message}`
    );
    return { success: false, message: error.message };
  }
}

/**
 * 等待用户输入后退出
 */
function waitForUserExit() {
  console.log("\x1b[33m按回车键退出...\x1b[0m");

  // 检查是否在TTY环境下
  if (process.stdin.isTTY) {
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on("data", process.exit.bind(process, 0));
  } else {
    // 非TTY环境下，直接退出
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  }
}

/**
 * 显示完成信息
 * @param {Array} results - 处理结果数组
 */
function showCompletionMessage(results) {
  console.log("\n\x1b[36m[完成] \x1b[32m所有处理完成！\x1b[0m");
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );

  const successCount = results.filter((r) => r.success).length;
  const failCount = results.length - successCount;

  console.log(`\x1b[32m成功处理: ${successCount} 个文件\x1b[0m`);
  if (failCount > 0) {
    console.log(`\x1b[31m处理失败: ${failCount} 个文件\x1b[0m`);
  }

  console.log(`\x1b[32mJSON文件已保存在: ${jsonBaseDir}\x1b[0m`);
  console.log(`\x1b[32mExcel文件已保存在: ${outputDir}\x1b[0m`);

  // 显示成功处理的文件详情
  results.forEach((result) => {
    if (result.success) {
      console.log(
        `  \x1b[32m✓ ${result.fileName}: ${result.dataCount} 条数据\x1b[0m`
      );
      if (result.excelFilePath) {
        console.log(`    \x1b[36mExcel文件:\x1b[0m ${result.excelFilePath}`);
      }
    } else {
      console.log(`  \x1b[31m✗ ${result.fileName}: ${result.message}\x1b[0m`);
    }
  });
}

/**
 * 功能2：生成材料清单
 */
async function generateMaterialList() {
  console.log("\n\x1b[36m[开始] \x1b[33m功能2: 生成4-材料清单...\x1b[0m");

  try {
    // JSON文件路径
    const jsonFilePath = path.join(jsonBaseDir, "分部分项成本测算.json");

    // 检查JSON文件是否存在
    if (!fs.existsSync(jsonFilePath)) {
      console.error(`\x1b[31m[错误] \x1b[0mJSON文件不存在: ${jsonFilePath}`);
      console.log(`\x1b[33m[提示] \x1b[0m请先运行功能1生成JSON文件。`);
      return false;
    }

    // 生成材料清单Excel文件
    const outputFilePath = await generateMaterialListExcel(
      jsonFilePath,
      inputDir,
      outputDir,
      "分部分项成本测算"
    );

    if (outputFilePath) {
      console.log(`\n\x1b[32m[完成] \x1b[0m材料清单生成完成！`);
      console.log(`\x1b[32m文件已保存在: ${outputFilePath}\x1b[0m`);
      return true;
    } else {
      console.log(`\n\x1b[31m[失败] \x1b[0m材料清单生成失败`);
      return false;
    }
  } catch (error) {
    console.error(
      `\x1b[31m[错误] \x1b[0m生成材料清单时发生错误: ${error.message}`
    );
    return false;
  }
}

/**
 * 显示功能选择菜单
 */
function showFunctionMenu() {
  console.log("\n\x1b[36m请选择要执行的功能:\x1b[0m");
  console.log("\x1b[33m1\x1b[0m - 读取分部分项成本测算数据（生成JSON和Excel）");
  console.log("\x1b[33m2\x1b[0m - 生成4-材料清单（基于已有JSON数据）");
  console.log("\x1b[33m3\x1b[0m - 执行完整流程（功能1 + 功能2）");
  console.log("\x1b[33mq\x1b[0m - 退出");
  console.log("\n\x1b[36m请输入选择 (1/2/3/q): \x1b[0m");
}

/**
 * 获取用户输入
 */
function getUserInput() {
  return new Promise((resolve) => {
    process.stdin.once("data", (data) => {
      resolve(data.toString().trim().toLowerCase());
    });
  });
}

/**
 * 主函数 - 功能1：读取分部分项成本测算数据
 */
async function calculateBill() {
  console.log(
    "\n\x1b[36m[开始] \x1b[33m功能1: 读取分部分项成本测算数据...\x1b[0m"
  );

  // 1. 读取input文件夹下的所有Excel文件
  const excelFiles = getExcelFiles(inputDir);

  // 检查是否有Excel文件
  if (excelFiles.length === 0) {
    console.error(
      `\x1b[31m[错误] \x1b[0m${inputDir} 文件夹中没有找到Excel文件！`
    );
    console.log(
      `\x1b[33m[提示] \x1b[0m请将Excel文件放在 input/bill-calculation 文件夹下。`
    );
    return false;
  }

  console.log(
    `\x1b[36m[信息] \x1b[0m找到 \x1b[32m${excelFiles.length}\x1b[0m 个Excel文件`
  );

  // 2. 处理每个Excel文件
  const results = [];
  for (const excelFile of excelFiles) {
    const result = await processExcelFile(excelFile);
    results.push(result);
  }

  // 显示完成信息
  showCompletionMessage(results);

  // 检查是否有成功的结果
  const hasSuccess = results.some((result) => result.success);
  return hasSuccess;
}

/**
 * 主入口函数
 */
async function main() {
  // 配置控制台输出
  configureConsole();

  try {
    // 确保目录存在
    ensureDirectories();

    // 显示欢迎信息
    showWelcomeMessage();

    while (true) {
      showFunctionMenu();
      const choice = await getUserInput();

      switch (choice) {
        case "1":
          await calculateBill();
          break;

        case "2":
          await generateMaterialList();
          break;

        case "3":
          console.log("\n\x1b[36m[开始] \x1b[33m执行完整流程...\x1b[0m");
          const step1Success = await calculateBill();
          if (step1Success) {
            await generateMaterialList();
          } else {
            console.log("\x1b[31m[跳过] \x1b[0m由于功能1失败，跳过功能2");
          }
          break;

        case "q":
          console.log("\n\x1b[36m[退出] \x1b[0m再见！");
          process.exit(0);
          break;

        default:
          console.log("\x1b[31m[错误] \x1b[0m无效的选择，请重新输入");
          break;
      }

      console.log("\n" + "=".repeat(60));
    }
  } catch (error) {
    console.error(
      `\n\x1b[31m[错误] \x1b[0m处理过程中出错: ${error.message}\x1b[0m`
    );
    waitForUserExit();
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  calculateBill,
  generateMaterialList,
  main,
};
