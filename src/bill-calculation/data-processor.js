// 新建数据处理器文件
const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

/**
 * 读取Excel文件中的指定表格数据
 * @param {string} filePath - Excel文件路径
 * @param {string} sheetName - 要读取的工作表名称
 * @returns {Array} 解析后的数据数组
 */
function readExcelSheet(filePath, sheetName) {
  // 读取Excel文件
  const workbook = XLSX.readFile(filePath);
  
  // 检查工作表是否存在
  if (!workbook.SheetNames.includes(sheetName)) {
    throw new Error(`工作簿中未找到名为 ${sheetName} 的工作表`);
  }
  
  // 获取工作表
  const worksheet = workbook.Sheets[sheetName];
  
  // 将工作表转换为JSON格式
  // header: 1 表示第一行作为标题
  const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  
  return data;
}

/**
 * 处理分部分项成本测算数据
 * @param {Array} data - 原始数据数组
 * @returns {Array} 处理后的平级结构JSON数据
 */
function processBillCalculationData(data) {
  // 过滤掉空行
  const filteredData = data.filter(row => row.length > 0 && row.some(cell => cell !== ''));
  
  // 提取表头
  const headers = filteredData[0];
  
  // 存储处理后的结果
  const result = [];
  
  // 当前一级、二级分类
  let currentLevel1 = null;
  let currentLevel2 = null;
  
  // 遍历数据行（从1开始，跳过标题行）
  for (let i = 1; i < filteredData.length; i++) {
    const row = filteredData[i];
    
    // 确保行数据完整
    if (row.length < 7) continue;
    
    // 只取A~G列数据
    const rowData = row.slice(0, 7);
    
    // 检查B列是否有值，判断是否为一级分类
    if (rowData[1] && rowData[1] !== '') {
      currentLevel1 = rowData[1];
      currentLevel2 = null; // 重置二级分类
    }
    
    // 检查C列是否有值，判断是否为二级分类
    if (rowData[2] && rowData[2] !== '') {
      currentLevel2 = rowData[2];
    }
    
    // 如果D列有值，则这是一个三级分类，需要保存到结果中
    if (rowData[3] && rowData[3] !== '') {
      result.push({
        level1: currentLevel1 || null, // 1级数据
        level2: currentLevel2 || null, // 2级数据
        level3: rowData[3] || null, // 3级数据
        a: rowData[0] || null, // A列数据
        b: rowData[1] || null, // B列数据
        c: rowData[2] || null, // C列数据
        d: rowData[3] || null, // D列数据
        e: rowData[4] || null, // E列数据
        f: rowData[5] || null, // F列数据
        g: rowData[6] || null, // G列数据
      });
    }
  }
  
  return result;
}

/**
 * 保存JSON数据到文件
 * @param {Array} data - 要保存的数据
 * @param {string} outputDir - 输出目录
 * @param {string} filename - 文件名
 */
function saveJsonData(data, outputDir, filename) {
  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // 构建输出文件路径
  const outputPath = path.join(outputDir, filename);
  
  // 写入JSON文件
  fs.writeFileSync(outputPath, JSON.stringify(data, null, 2), 'utf8');
  
  console.log(`\x1b[32m[成功] \x1b[0m数据已保存到 ${outputPath}`);
}

module.exports = {
  readExcelSheet,
  processBillCalculationData,
  saveJsonData
};